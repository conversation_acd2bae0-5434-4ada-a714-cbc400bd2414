"""
Django management command to sync tracking status for Rapidshyp shipments
"""

import logging
from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.conf import settings
from datetime import timedelta

from shipping.models import RapidshypShipment
from shipping.services.shipping_service import ShippingService


class Command(BaseCommand):
    help = 'Sync tracking status for Rapidshyp shipments'

    def add_arguments(self, parser):
        parser.add_argument(
            '--shipment-id',
            type=str,
            help='Sync specific shipment by Rapidshyp order ID',
        )
        parser.add_argument(
            '--awb',
            type=str,
            help='Sync specific shipment by AWB number',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Sync shipments from last N days (default: 7)',
        )
        parser.add_argument(
            '--status',
            type=str,
            choices=['PENDING', 'PICKED', 'IN_TRANSIT', 'OUT_FOR_DELIVERY', 'DELIVERED', 'RTO'],
            help='Sync only shipments with specific status',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force sync even if recently updated',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without making changes',
        )

    def handle(self, *args, **options):
        if not getattr(settings, 'RAPIDSHYP_ENABLED', False):
            raise CommandError('Rapidshyp integration is not enabled')

        # Setup logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)

        # Initialize shipping service
        shipping_service = ShippingService()

        if not shipping_service.is_rapidshyp_available():
            raise CommandError('Rapidshyp service is not available')

        # Build queryset based on options
        queryset = RapidshypShipment.objects.all()

        if options['shipment_id']:
            queryset = queryset.filter(rapidshyp_order_id=options['shipment_id'])
        elif options['awb']:
            queryset = queryset.filter(awb_number=options['awb'])
        else:
            # Filter by date range
            days_ago = timezone.now() - timedelta(days=options['days'])
            queryset = queryset.filter(created_at__gte=days_ago)

            # Filter by status if specified
            if options['status']:
                queryset = queryset.filter(current_status=options['status'])

            # Exclude delivered and returned shipments unless forced
            if not options['force']:
                queryset = queryset.exclude(current_status__in=['DELIVERED', 'RTO', 'CANCELLED'])

        # Order by creation date
        queryset = queryset.order_by('-created_at')

        total_shipments = queryset.count()
        
        if total_shipments == 0:
            self.stdout.write(
                self.style.WARNING('No shipments found matching the criteria')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(f'Found {total_shipments} shipments to sync')
        )

        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
            for shipment in queryset[:10]:  # Show first 10
                self.stdout.write(
                    f'Would sync: {shipment.rapidshyp_order_id} '
                    f'(AWB: {shipment.awb_number}, Status: {shipment.current_status})'
                )
            if total_shipments > 10:
                self.stdout.write(f'... and {total_shipments - 10} more')
            return

        # Perform sync
        success_count = 0
        error_count = 0
        unchanged_count = 0

        for i, shipment in enumerate(queryset, 1):
            try:
                self.stdout.write(
                    f'[{i}/{total_shipments}] Syncing {shipment.rapidshyp_order_id}...',
                    ending=''
                )

                old_status = shipment.current_status
                success = shipping_service.sync_tracking_status(shipment)

                if success:
                    # Refresh from database to get updated status
                    shipment.refresh_from_db()
                    new_status = shipment.current_status

                    if old_status != new_status:
                        self.stdout.write(
                            self.style.SUCCESS(f' ✓ Updated: {old_status} → {new_status}')
                        )
                        success_count += 1
                    else:
                        self.stdout.write(
                            self.style.WARNING(' - No change')
                        )
                        unchanged_count += 1
                else:
                    self.stdout.write(
                        self.style.ERROR(' ✗ Failed')
                    )
                    error_count += 1

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f' ✗ Error: {str(e)}')
                )
                error_count += 1
                logger.error(f'Error syncing {shipment.rapidshyp_order_id}: {e}')

        # Summary
        self.stdout.write('\n' + '='*50)
        self.stdout.write(
            self.style.SUCCESS(f'Sync completed:')
        )
        self.stdout.write(f'  Total processed: {total_shipments}')
        self.stdout.write(f'  Successfully updated: {success_count}')
        self.stdout.write(f'  No changes: {unchanged_count}')
        self.stdout.write(f'  Errors: {error_count}')

        if error_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f'\n{error_count} shipments failed to sync. '
                    'Check logs for details.'
                )
            )
