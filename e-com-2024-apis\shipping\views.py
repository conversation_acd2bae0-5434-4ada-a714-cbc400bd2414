"""
Shipping API views for Rapidshyp integration
"""

import json
import logging
from rest_framework import status, generics, permissions
from rest_framework.decorators import api_view, permission_classes, throttle_classes
from rest_framework.response import Response
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle
from django.shortcuts import get_object_or_404
from django.conf import settings
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt

from orders.models import Order
from .models import RapidshypShipment, RapidshypConfiguration
from .services import ShippingService
from .serializers import (
    ShippingRateRequestSerializer,
    ShippingRateResponseSerializer,
    OrderTrackingRequestSerializer,
    OrderTrackingResponseSerializer,
    RapidshypShipmentSerializer,
    RapidshypConfigurationSerializer,
    ServiceStatusSerializer,
    PincodeValidationSerializer,
    PincodeValidationResponseSerializer,
    BulkTrackingRequestSerializer,
    BulkTrackingResponseSerializer
)
from .utils import validate_pincode as validate_pincode_format


logger = logging.getLogger(__name__)


class ShippingRateThrottle(UserRateThrottle):
    """Custom throttle for shipping rate calculations"""
    scope = 'shipping_rates'
    rate = '60/hour'  # 1 per minute


class TrackingThrottle(UserRateThrottle):
    """Custom throttle for tracking requests"""
    scope = 'tracking'
    rate = '120/hour'  # 2 per minute


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@throttle_classes([ShippingRateThrottle])
def calculate_shipping_rates(request):
    """
    Calculate shipping rates using Rapidshyp with fallback to existing methods

    POST /api/v1/shipping/calculate-rates/
    """
    serializer = ShippingRateRequestSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # Get validated data
        data = serializer.validated_data
        pickup_pincode = data.get('pickup_pincode') or getattr(settings, 'RAPIDSHYP_DEFAULT_PICKUP_PINCODE', '110001')

        # Initialize shipping service
        shipping_service = ShippingService()

        # Calculate rates
        rates_response = shipping_service.get_shipping_rates(
            pickup_pincode=pickup_pincode,
            delivery_pincode=data['delivery_pincode'],
            weight=float(data['weight']),
            cod=data['cod'],
            total_value=float(data['total_value'])
        )

        # Serialize response
        response_serializer = ShippingRateResponseSerializer(rates_response)

        logger.info(f"Shipping rates calculated for {data['delivery_pincode']}: "
                   f"{len(rates_response.get('rapidshyp_rates', []))} Rapidshyp + "
                   f"{len(rates_response.get('existing_methods', []))} existing methods")

        return Response(response_serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Shipping rate calculation failed: {e}")

        # Return fallback response
        try:
            shipping_service = ShippingService()
            fallback_response = shipping_service.fallback_service.get_fallback_response(
                delivery_pincode=data['delivery_pincode'],
                weight=float(data['weight']),
                cod=data['cod']
            )

            response_serializer = ShippingRateResponseSerializer(fallback_response)
            return Response(response_serializer.data, status=status.HTTP_200_OK)

        except Exception as fallback_error:
            logger.error(f"Fallback shipping rates failed: {fallback_error}")
            return Response({
                'success': False,
                'error': 'Shipping rate calculation temporarily unavailable',
                'message': 'Please try again later'
            }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
@throttle_classes([TrackingThrottle])
def track_order(request, order_id):
    """
    Get tracking information for an order

    GET /api/v1/shipping/track/{order_id}/
    """
    try:
        # Get order (ensure user owns it)
        order = get_object_or_404(Order, id=order_id, user=request.user)

        # Initialize shipping service
        shipping_service = ShippingService()

        # Check if order has Rapidshyp shipment
        if hasattr(order, 'rapidshyp_shipment') and order.rapidshyp_shipment:
            shipment = order.rapidshyp_shipment
            tracking_info = shipping_service.get_tracking_info(shipment)

            if tracking_info.get('success'):
                response_data = {
                    'success': True,
                    'tracking_available': True,
                    'source': 'rapidshyp',
                    'shipment_id': tracking_info['shipment_id'],
                    'rapidshyp_order_id': tracking_info['rapidshyp_order_id'],
                    'awb_number': tracking_info['awb_number'],
                    'courier_name': tracking_info['courier_name'],
                    'current_status': tracking_info['current_status'],
                    'status_description': tracking_info['status_description'],
                    'tracking_url': tracking_info['tracking_url'],
                    'expected_delivery_date': tracking_info['expected_delivery_date'],
                    'actual_delivery_date': tracking_info['actual_delivery_date'],
                    'tracking_events': tracking_info['tracking_events']
                }
            else:
                # Rapidshyp tracking failed, use basic info
                response_data = {
                    'success': True,
                    'tracking_available': True,
                    'source': 'rapidshyp_basic',
                    'shipment_id': str(shipment.id),
                    'rapidshyp_order_id': shipment.rapidshyp_order_id,
                    'awb_number': shipment.awb_number,
                    'courier_name': shipment.courier_name,
                    'current_status': shipment.current_status,
                    'status_description': shipment.status_description,
                    'error': tracking_info.get('error', 'Live tracking temporarily unavailable')
                }
        else:
            # Standard order tracking
            response_data = {
                'success': True,
                'tracking_available': bool(order.tracking_number),
                'source': 'standard',
                'tracking_number': order.tracking_number,
                'order_status': order.status,
                'estimated_delivery_date': order.estimated_delivery_date.isoformat() if order.estimated_delivery_date else None
            }

        response_serializer = OrderTrackingResponseSerializer(response_data)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    except Order.DoesNotExist:
        return Response({
            'success': False,
            'error': 'Order not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Order tracking failed for {order_id}: {e}")
        return Response({
            'success': False,
            'error': 'Tracking information temporarily unavailable'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def validate_pincode(request):
    """
    Validate pincode and check serviceability

    POST /api/v1/shipping/validate-pincode/
    """
    serializer = PincodeValidationSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        pincode = serializer.validated_data['pincode']

        # Basic validation
        is_valid = validate_pincode_format(pincode)

        # Check serviceability with Rapidshyp
        is_serviceable = False
        message = "Invalid pincode format"

        if is_valid:
            try:
                shipping_service = ShippingService()
                if shipping_service.is_rapidshyp_available():
                    # Try Rapidshyp serviceability check
                    pickup_pincode = getattr(settings, 'RAPIDSHYP_DEFAULT_PICKUP_PINCODE', '110001')
                    rates_response = shipping_service.get_shipping_rates(
                        pickup_pincode=pickup_pincode,
                        delivery_pincode=pincode,
                        weight=1.0
                    )
                    is_serviceable = rates_response.get('success', False) and len(rates_response.get('rapidshyp_rates', [])) > 0
                    message = "Pincode is serviceable" if is_serviceable else "Pincode not serviceable via Rapidshyp"
                else:
                    # Fallback - assume serviceable
                    is_serviceable = shipping_service.fallback_service.is_pincode_serviceable(pincode)
                    message = "Pincode is serviceable (standard shipping)" if is_serviceable else "Pincode not serviceable"
            except Exception as e:
                logger.warning(f"Serviceability check failed for {pincode}: {e}")
                # Fallback to basic validation
                is_serviceable = True
                message = "Pincode format is valid"

        response_data = {
            'pincode': pincode,
            'is_valid': is_valid,
            'is_serviceable': is_serviceable,
            'message': message
        }

        response_serializer = PincodeValidationResponseSerializer(response_data)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Pincode validation failed: {e}")
        return Response({
            'success': False,
            'error': 'Pincode validation temporarily unavailable'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def service_status(request):
    """
    Get shipping service status

    GET /api/v1/shipping/status/
    """
    try:
        shipping_service = ShippingService()
        status_data = shipping_service.get_service_status()

        response_serializer = ServiceStatusSerializer(status_data)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Service status check failed: {e}")
        return Response({
            'error': 'Service status check failed'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@throttle_classes([TrackingThrottle])
def bulk_track_orders(request):
    """
    Track multiple orders in bulk

    POST /api/v1/shipping/bulk-track/
    """
    serializer = BulkTrackingRequestSerializer(data=request.data)

    if not serializer.is_valid():
        return Response({
            'success': False,
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        order_ids = serializer.validated_data['order_ids']

        # Get user's orders
        orders = Order.objects.filter(
            id__in=order_ids,
            user=request.user
        ).prefetch_related('rapidshyp_shipment')

        tracking_data = {}
        errors = {}
        processed_count = 0
        error_count = 0

        shipping_service = ShippingService()

        for order in orders:
            try:
                order_id_str = str(order.id)

                if hasattr(order, 'rapidshyp_shipment') and order.rapidshyp_shipment:
                    # Rapidshyp tracking
                    shipment = order.rapidshyp_shipment
                    tracking_info = shipping_service.get_tracking_info(shipment)

                    if tracking_info.get('success'):
                        tracking_data[order_id_str] = {
                            'source': 'rapidshyp',
                            'awb_number': tracking_info['awb_number'],
                            'courier_name': tracking_info['courier_name'],
                            'current_status': tracking_info['current_status'],
                            'status_description': tracking_info['status_description']
                        }
                    else:
                        tracking_data[order_id_str] = {
                            'source': 'rapidshyp_basic',
                            'awb_number': shipment.awb_number,
                            'courier_name': shipment.courier_name,
                            'current_status': shipment.current_status,
                            'status_description': shipment.status_description
                        }
                else:
                    # Standard tracking
                    tracking_data[order_id_str] = {
                        'source': 'standard',
                        'tracking_number': order.tracking_number,
                        'order_status': order.status
                    }

                processed_count += 1

            except Exception as e:
                error_count += 1
                errors[str(order.id)] = str(e)
                logger.error(f"Bulk tracking failed for order {order.id}: {e}")

        # Handle orders not found
        found_order_ids = {str(order.id) for order in orders}
        for order_id in order_ids:
            order_id_str = str(order_id)
            if order_id_str not in found_order_ids:
                error_count += 1
                errors[order_id_str] = "Order not found"

        response_data = {
            'success': True,
            'tracking_data': tracking_data,
            'errors': errors,
            'processed_count': processed_count,
            'error_count': error_count
        }

        response_serializer = BulkTrackingResponseSerializer(response_data)
        return Response(response_serializer.data, status=status.HTTP_200_OK)

    except Exception as e:
        logger.error(f"Bulk tracking failed: {e}")
        return Response({
            'success': False,
            'error': 'Bulk tracking temporarily unavailable'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def health_check(request):
    """
    Health check endpoint for shipping service

    GET /api/v1/shipping/health/
    """
    try:
        shipping_service = ShippingService()

        # Basic health checks
        health_data = {
            'status': 'healthy',
            'timestamp': timezone.now().isoformat(),
            'rapidshyp_enabled': shipping_service.rapidshyp_enabled,
            'rapidshyp_available': False,
            'fallback_available': True,
            'database_accessible': True
        }

        # Test Rapidshyp availability (with timeout)
        try:
            health_data['rapidshyp_available'] = shipping_service.is_rapidshyp_available()
        except Exception as e:
            logger.warning(f"Rapidshyp health check failed: {e}")
            health_data['rapidshyp_available'] = False

        # Test database access
        try:
            RapidshypConfiguration.objects.exists()
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            health_data['database_accessible'] = False
            health_data['status'] = 'degraded'

        # Determine overall status
        if not health_data['database_accessible']:
            health_data['status'] = 'unhealthy'
        elif not health_data['rapidshyp_available'] and shipping_service.rapidshyp_enabled:
            health_data['status'] = 'degraded'

        status_code = status.HTTP_200_OK
        if health_data['status'] == 'unhealthy':
            status_code = status.HTTP_503_SERVICE_UNAVAILABLE
        elif health_data['status'] == 'degraded':
            status_code = status.HTTP_200_OK  # Still functional with fallback

        return Response(health_data, status=status_code)

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return Response({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': timezone.now().isoformat()
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)


@csrf_exempt
@api_view(['POST'])
@permission_classes([permissions.AllowAny])
def rapidshyp_webhook(request):
    """
    Handle Rapidshyp webhook notifications for tracking updates

    POST /api/v1/shipping/webhook/
    """
    try:
        # Get request data
        payload = request.body.decode('utf-8')

        # Get signature from headers
        signature = request.headers.get('X-Rapidshyp-Signature')
        timestamp = request.headers.get('X-Rapidshyp-Timestamp')

        if not signature:
            logger.error("No signature provided in Rapidshyp webhook")
            return Response({"status": "error", "message": "No signature provided"}, status=400)

        # Verify webhook signature
        from .utils import verify_webhook_signature
        webhook_secret = getattr(settings, 'RAPIDSHYP_WEBHOOK_SECRET', '')

        if not verify_webhook_signature(payload, signature, webhook_secret):
            logger.error("Invalid signature in Rapidshyp webhook")
            return Response({"status": "error", "message": "Invalid signature"}, status=400)

        # Verify timestamp (prevent replay attacks)
        if timestamp:
            try:
                webhook_time = int(timestamp)
                current_time = int(timezone.now().timestamp())
                max_diff = getattr(settings, 'RAPIDSHYP_WEBHOOK_SETTINGS', {}).get('MAX_TIMESTAMP_DIFF', 300)

                if abs(current_time - webhook_time) > max_diff:
                    logger.error(f"Webhook timestamp too old: {webhook_time} vs {current_time}")
                    return Response({"status": "error", "message": "Timestamp too old"}, status=400)
            except (ValueError, TypeError):
                logger.warning("Invalid timestamp in webhook header")

        # Parse webhook data
        try:
            webhook_data = json.loads(payload)
        except json.JSONDecodeError:
            logger.error("Invalid JSON in Rapidshyp webhook payload")
            return Response({"status": "error", "message": "Invalid JSON"}, status=400)

        # Process webhook
        result = process_rapidshyp_webhook(webhook_data)

        if result.get('success'):
            logger.info(f"Rapidshyp webhook processed successfully: {result.get('message')}")
            return Response({"status": "success", "message": result.get('message')})
        else:
            logger.error(f"Rapidshyp webhook processing failed: {result.get('error')}")
            return Response({"status": "error", "message": result.get('error')}, status=400)

    except Exception as e:
        logger.error(f"Error in Rapidshyp webhook: {str(e)}")
        # Always return 200 to prevent Rapidshyp from retrying
        return Response({"status": "error", "message": "Internal server error"})


def process_rapidshyp_webhook(webhook_data):
    """
    Process Rapidshyp webhook data and update tracking information

    Args:
        webhook_data: Parsed webhook JSON data

    Returns:
        dict: Processing result
    """
    try:
        # Extract key information from webhook
        event_type = webhook_data.get('event_type', '')
        awb_number = webhook_data.get('awb_number', '')
        order_id = webhook_data.get('order_id', '')
        status_code = webhook_data.get('status', '')
        status_description = webhook_data.get('status_description', '')
        location = webhook_data.get('location', '')
        event_timestamp = webhook_data.get('timestamp', '')

        if not (awb_number or order_id):
            return {'success': False, 'error': 'No AWB number or order ID provided'}

        # Find the shipment
        shipment = None
        if awb_number:
            try:
                shipment = RapidshypShipment.objects.get(awb_number=awb_number)
            except RapidshypShipment.DoesNotExist:
                pass

        if not shipment and order_id:
            try:
                shipment = RapidshypShipment.objects.get(rapidshyp_order_id=order_id)
            except RapidshypShipment.DoesNotExist:
                pass

        if not shipment:
            return {'success': False, 'error': f'Shipment not found for AWB: {awb_number}, Order: {order_id}'}

        # Update shipment status
        old_status = shipment.current_status
        if status_code and status_code != old_status:
            shipment.current_status = status_code
            shipment.status_description = status_description
            shipment.save()

            # Create tracking event
            from .models import TrackingEvent
            TrackingEvent.objects.create(
                shipment=shipment,
                status=status_code,
                status_description=status_description,
                location=location,
                event_timestamp=timezone.now() if not event_timestamp else timezone.datetime.fromisoformat(event_timestamp.replace('Z', '+00:00')),
                remarks=f"Webhook update: {event_type}"
            )

            # Update order status if needed
            shipping_service = ShippingService()
            shipping_service._update_order_status_from_shipment(shipment)

            logger.info(f"Webhook updated shipment {shipment.rapidshyp_order_id}: {old_status} -> {status_code}")

            return {
                'success': True,
                'message': f'Shipment {shipment.rapidshyp_order_id} updated from {old_status} to {status_code}'
            }
        else:
            return {'success': True, 'message': 'No status change required'}

    except Exception as e:
        logger.error(f"Webhook processing error: {e}")
        return {'success': False, 'error': str(e)}
