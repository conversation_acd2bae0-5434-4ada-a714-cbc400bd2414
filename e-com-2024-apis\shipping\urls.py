"""
Shipping URL patterns for Rapidshyp integration
"""

from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

# Create router for ViewSets (if we add any later)
router = DefaultRouter()

# URL patterns for shipping API
urlpatterns = [
    # Shipping rate calculation
    path('calculate-rates/', views.calculate_shipping_rates, name='calculate-shipping-rates'),

    # Order tracking
    path('track/<uuid:order_id>/', views.track_order, name='track-order'),
    path('bulk-track/', views.bulk_track_orders, name='bulk-track-orders'),

    # Pincode validation
    path('validate-pincode/', views.validate_pincode, name='validate-pincode'),

    # Webhook endpoint
    path('webhook/', views.rapidshyp_webhook, name='rapidshyp-webhook'),

    # Service status and health
    path('status/', views.service_status, name='service-status'),
    path('health/', views.health_check, name='health-check'),

    # Include router URLs (for future ViewSets)
    path('', include(router.urls)),
]

# Add app name for namespacing
app_name = 'shipping'
