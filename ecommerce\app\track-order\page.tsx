"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  Package, 
  Search, 
  AlertTriangle,
  Truck,
  MapPin
} from 'lucide-react';
import MainHOF from '@/layout/MainHOF';
import { OrderTrackingInterface } from '@/components/shipping/OrderTrackingInterface';
import { ShippingErrorBoundary } from '@/components/shipping/ShippingErrorBoundary';
import useApi from '@/hooks/useApi';
import { MAIN_URL, ORDERS } from '@/constant/urls';

interface TrackingSearchResult {
  success: boolean;
  order_found: boolean;
  order_id?: string;
  tracking_available?: boolean;
  message?: string;
  error?: string;
}

const TrackOrderPage = () => {
  const [searchInput, setSearchInput] = useState('');
  const [searchType, setSearchType] = useState<'order_id' | 'tracking_number'>('order_id');
  const [searchResult, setSearchResult] = useState<TrackingSearchResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [foundOrderId, setFoundOrderId] = useState<string | null>(null);

  const { read } = useApi<TrackingSearchResult>(MAIN_URL);

  const handleSearch = async () => {
    if (!searchInput.trim()) {
      setSearchResult({
        success: false,
        order_found: false,
        error: 'Please enter an order ID or tracking number'
      });
      return;
    }

    setLoading(true);
    setSearchResult(null);
    setFoundOrderId(null);

    try {
      // Search for order by order ID or tracking number
      const searchEndpoint = searchType === 'order_id' 
        ? `${ORDERS}${searchInput.trim()}/public-tracking/`
        : `${ORDERS}search-by-tracking/${searchInput.trim()}/`;

      const result = await read(searchEndpoint);

      if (typeof result === 'string') {
        setSearchResult({
          success: false,
          order_found: false,
          error: result
        });
      } else if (result.success && result.order_found) {
        setSearchResult(result);
        setFoundOrderId(result.order_id || searchInput.trim());
      } else {
        setSearchResult({
          success: false,
          order_found: false,
          message: result.message || 'Order not found or tracking not available'
        });
      }
    } catch (error) {
      setSearchResult({
        success: false,
        order_found: false,
        error: 'Failed to search for order. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <MainHOF>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold mb-4 flex items-center justify-center">
              <Package className="h-8 w-8 mr-3 text-blue-600" />
              Track Your Order
            </h1>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Enter your order ID or tracking number to get real-time updates on your shipment status.
            </p>
          </div>

          {/* Search Section */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Search className="h-5 w-5 mr-2" />
                Find Your Order
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Search Type Selection */}
              <div className="flex gap-4">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="order_id"
                    checked={searchType === 'order_id'}
                    onChange={(e) => setSearchType(e.target.value as 'order_id')}
                    className="mr-2"
                  />
                  Order ID
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="searchType"
                    value="tracking_number"
                    checked={searchType === 'tracking_number'}
                    onChange={(e) => setSearchType(e.target.value as 'tracking_number')}
                    className="mr-2"
                  />
                  Tracking Number
                </label>
              </div>

              {/* Search Input */}
              <div className="flex gap-2">
                <Input
                  type="text"
                  placeholder={
                    searchType === 'order_id' 
                      ? "Enter your order ID (e.g., 12345678-1234-1234-1234-123456789012)"
                      : "Enter your tracking number (e.g., AWB123456789)"
                  }
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="flex-1"
                />
                <Button 
                  onClick={handleSearch}
                  disabled={loading || !searchInput.trim()}
                  className="px-6"
                >
                  {loading ? (
                    <div className="w-4 h-4 border-t-2 border-b-2 border-current rounded-full animate-spin"></div>
                  ) : (
                    <>
                      <Search className="h-4 w-4 mr-2" />
                      Track
                    </>
                  )}
                </Button>
              </div>

              {/* Search Result Messages */}
              {searchResult && !searchResult.success && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {searchResult.error || searchResult.message || 'Order not found'}
                  </AlertDescription>
                </Alert>
              )}

              {searchResult && searchResult.success && !searchResult.order_found && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {searchResult.message || 'No tracking information available for this order'}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Tracking Results */}
          {foundOrderId && searchResult?.success && searchResult?.order_found && (
            <div className="mb-8">
              <ShippingErrorBoundary>
                <OrderTrackingInterface
                  orderId={foundOrderId}
                  autoRefresh={true}
                  refreshInterval={60000}
                />
              </ShippingErrorBoundary>
            </div>
          )}

          {/* Help Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Need Help?
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-semibold mb-2">Where to find your Order ID?</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Check your order confirmation email</li>
                    <li>• Look in your account order history</li>
                    <li>• Find it on your invoice or receipt</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-semibold mb-2">Where to find your Tracking Number?</h3>
                  <ul className="text-sm text-gray-600 space-y-1">
                    <li>• Check your shipping confirmation email</li>
                    <li>• Look for AWB number in order details</li>
                    <li>• Contact customer support if needed</li>
                  </ul>
                </div>
              </div>
              
              <Separator />
              
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  Still having trouble tracking your order?
                </p>
                <Button variant="outline" className="flex items-center mx-auto">
                  <Truck className="h-4 w-4 mr-2" />
                  Contact Support
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainHOF>
  );
};

export default TrackOrderPage;
