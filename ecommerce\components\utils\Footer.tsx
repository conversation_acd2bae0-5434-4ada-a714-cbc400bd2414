"use client";

import {
  Facebook,
  Instagram,
  Twitter,
  Phone,
  Mail,
  MapPin,
  Send,
  Heart,
  ShoppingBag,
  User,
  HelpCircle,
  Home,
  ShoppingCart,
  CreditCard,
  Package,
  Shield,
} from "lucide-react";
import Link from "next/link";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useEffect, useState } from "react";
import { CATEGORIES, MAIN_URL } from "@/constant/urls";
import useApi from "@/hooks/useApi";

const Footer = () => {
  const [categoryData, setCategoryData] = useState<any[]>([]);
  const { data, loading, read } = useApi(MAIN_URL);

  useEffect(() => {
    const fetchCategories = async () => {
      await read(CATEGORIES);
    };
    fetchCategories();
  }, []);

  useEffect(() => {
    if (data && Array.isArray(data)) {
      setCategoryData(data.slice(0, 5)); // Limit to 5 categories
    }
  }, [data]);

  const socialLinks = [
    { icon: Facebook, href: "#", label: "Facebook", color: "bg-blue-600" },
    { icon: Instagram, href: "#", label: "Instagram", color: "bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500" },
    { icon: Twitter, href: "#", label: "Twitter", color: "bg-sky-500" },
  ];

  const quickLinks = [
    { text: "About Us", href: "#", icon: User },
    { text: "Shop", href: "/shop", icon: ShoppingBag },
    { text: "Track Order", href: "/track-order", icon: Package },
    { text: "Contact Us", href: "/contact-us", icon: Mail },
    { text: "Shipping Policy", href: "/shipping-policy", icon: Package },
    { text: "Return Policy", href: "/return-policy", icon: Package },
    { text: "Refund Policy", href: "/refund-policy", icon: CreditCard },
    { text: "Privacy Policy", href: "/privacy-policy", icon: Shield },
    { text: "Terms & Conditions", href: "/terms-and-conditions", icon: HelpCircle },
  ];

  return (
    <footer className="bg-theme-footer text-theme-text-secondary border-t border-gray-700/30">
      {/* Top Accent Bar */}
      <div className="h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary/80"></div>

      <div className="container mx-auto px-4 py-16">
        {/* Main Footer Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10 mb-16">
          {/* Brand Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <img src="/logotriumph.png" alt="Triumph Enterprises Logo" className="h-10 w-auto" />
              <h2 className="text-2xl font-bold text-white">TRIUMPH ENTERPRISES</h2>
            </div>
            <p className="text-gray-300">
              Committed to providing quality products at competitive prices.
            </p>
            <div className="flex space-x-3 mt-6">
              {socialLinks.map(({ icon: Icon, href, label, color }) => (
                <Link
                  key={label}
                  href={href}
                  className={`${color} text-white p-2.5 rounded-full hover:opacity-90 hover:shadow-md transition-all duration-300 flex items-center justify-center`}
                  aria-label={label}
                >
                  <Icon className="w-4 h-4" />
                </Link>
              ))}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-1">
                Quick Links
              </h3>
              <div className="h-1 w-8 bg-theme-accent-primary rounded-full"></div>
            </div>
            <ul className="space-y-3.5">
              {quickLinks.map(({ text, href, icon: Icon }) => (
                <li key={text}>
                  <Link
                    href={href}
                    className="text-gray-300 hover:text-theme-accent-primary transition-colors duration-300 flex items-center group"
                  >
                    <div className="bg-white/10 p-1.5 rounded-full mr-3 group-hover:bg-theme-accent-primary/20 transition-colors duration-300">
                      <Icon className="w-4 h-4 text-gray-300 group-hover:text-theme-accent-primary transition-colors duration-300" />
                    </div>
                    <span className="group-hover:translate-x-1 transition-transform duration-300">{text}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-white">
              Customer Support
            </h3>
            <div className="space-y-4">
              <div className="flex items-center space-x-2 text-gray-300">
                <Phone className="w-5 h-5 text-theme-accent-primary" />
                <span>+91 9848486452</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-300">
                <Mail className="w-5 h-5 text-theme-accent-primary" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="py-10 px-6 bg-white/5 rounded-xl mb-16 border border-white/10">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl md:text-3xl font-bold text-white mb-3">Subscribe to our Newsletter</h2>
            <p className="text-gray-300 mb-6">Stay updated with the latest products, offers and news</p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Your email address"
                className="flex-grow bg-white/10 border-white/20 focus:border-theme-accent-primary focus:ring-theme-accent-primary text-white placeholder:text-gray-400"
              />
              <Button className="bg-theme-accent-primary hover:bg-theme-accent-hover text-white shadow-md hover:shadow-lg transition-all duration-300">
                <Send className="h-4 w-4 mr-2" />
                Subscribe
              </Button>
            </div>
          </div>
        </div>

        {/* Trust Badges */}
        <div className="flex flex-wrap justify-center gap-8 mb-12">
          <div className="flex items-center space-x-2 text-white">
            <div className="bg-theme-accent-primary/20 p-2 rounded-full">
              <Shield className="w-5 h-5 text-theme-accent-primary" />
            </div>
            <span className="font-medium">Secure Payment</span>
          </div>
          <div className="flex items-center space-x-2 text-white">
            <div className="bg-theme-accent-primary/20 p-2 rounded-full">
              <Package className="w-5 h-5 text-theme-accent-primary" />
            </div>
            <span className="font-medium">Fast Shipping</span>
          </div>
          <div className="flex items-center space-x-2 text-white">
            <div className="bg-theme-accent-primary/20 p-2 rounded-full">
              <CreditCard className="w-5 h-5 text-theme-accent-primary" />
            </div>
            <span className="font-medium">Easy Returns</span>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="pt-8 border-t border-gray-700/30">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-gray-400">
              © 2024 TRIUMPH ENTERPRISES. All rights reserved.
            </p>
            <div className="flex items-center space-x-6">
              <img src="https://cdn-icons-png.flaticon.com/512/196/196566.png" alt="Visa" className="h-7 opacity-50 hover:opacity-100 transition-opacity duration-300" />
              <img src="https://cdn-icons-png.flaticon.com/512/196/196561.png" alt="Mastercard" className="h-7 opacity-50 hover:opacity-100 transition-opacity duration-300" />
              <img src="https://cdn-icons-png.flaticon.com/512/196/196565.png" alt="PayPal" className="h-7 opacity-50 hover:opacity-100 transition-opacity duration-300" />
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
