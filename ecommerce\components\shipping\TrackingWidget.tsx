/**
 * Compact tracking widget for displaying quick tracking info
 */

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Package, 
  Truck, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface TrackingWidgetProps {
  orderId: string;
  trackingNumber?: string;
  status: string;
  courierName?: string;
  estimatedDelivery?: string;
  isRapidshyp?: boolean;
  trackingUrl?: string;
  className?: string;
  compact?: boolean;
}

const getStatusIcon = (status: string) => {
  const normalizedStatus = status.toUpperCase();
  
  switch (normalizedStatus) {
    case 'DELIVERED':
      return <CheckCircle className="h-4 w-4 text-green-600" />;
    case 'OUT_FOR_DELIVERY':
    case 'OUT FOR DELIVERY':
      return <Truck className="h-4 w-4 text-blue-600" />;
    case 'IN_TRANSIT':
    case 'IN TRANSIT':
    case 'SHIPPED':
      return <Package className="h-4 w-4 text-orange-600" />;
    case 'PICKED':
    case 'PICKUP':
      return <MapPin className="h-4 w-4 text-purple-600" />;
    case 'PENDING':
    case 'PROCESSING':
      return <Clock className="h-4 w-4 text-yellow-600" />;
    case 'CANCELLED':
    case 'RTO':
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
    default:
      return <Package className="h-4 w-4 text-gray-600" />;
  }
};

const getStatusColor = (status: string) => {
  const normalizedStatus = status.toUpperCase();
  
  switch (normalizedStatus) {
    case 'DELIVERED':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'OUT_FOR_DELIVERY':
    case 'OUT FOR DELIVERY':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'IN_TRANSIT':
    case 'IN TRANSIT':
    case 'SHIPPED':
      return 'bg-orange-100 text-orange-700 border-orange-200';
    case 'PICKED':
    case 'PICKUP':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'PENDING':
    case 'PROCESSING':
      return 'bg-yellow-100 text-yellow-700 border-yellow-200';
    case 'CANCELLED':
    case 'RTO':
      return 'bg-red-100 text-red-700 border-red-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};

const formatStatus = (status: string) => {
  return status.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

const formatDate = (dateString: string) => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  } catch {
    return dateString;
  }
};

export const TrackingWidget: React.FC<TrackingWidgetProps> = ({
  orderId,
  trackingNumber,
  status,
  courierName,
  estimatedDelivery,
  isRapidshyp = false,
  trackingUrl,
  className,
  compact = false
}) => {
  if (compact) {
    return (
      <div className={cn("flex items-center justify-between p-3 bg-gray-50 rounded-lg border", className)}>
        <div className="flex items-center space-x-3">
          {getStatusIcon(status)}
          <div>
            <p className="text-sm font-medium text-gray-900">
              {formatStatus(status)}
            </p>
            {trackingNumber && (
              <p className="text-xs text-gray-500">
                {trackingNumber}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {isRapidshyp && (
            <Badge variant="secondary" className="text-xs">
              Live Tracking
            </Badge>
          )}
          <Link href={`/track-order?order=${orderId}`}>
            <Button variant="outline" size="sm">
              Track
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            {getStatusIcon(status)}
            <div>
              <h3 className="font-semibold text-gray-900">
                Shipment Tracking
              </h3>
              <p className="text-sm text-gray-500">
                Order #{orderId.slice(-8)}
              </p>
            </div>
          </div>
          
          {isRapidshyp && (
            <Badge variant="default" className="bg-green-100 text-green-700">
              Live Tracking
            </Badge>
          )}
        </div>

        <div className="space-y-3">
          {/* Status */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-600">Status:</span>
            <Badge className={getStatusColor(status)}>
              {formatStatus(status)}
            </Badge>
          </div>

          {/* Tracking Number */}
          {trackingNumber && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Tracking:</span>
              <span className="text-sm font-mono text-gray-900">
                {trackingNumber}
              </span>
            </div>
          )}

          {/* Courier */}
          {courierName && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Courier:</span>
              <span className="text-sm text-gray-900">
                {courierName}
              </span>
            </div>
          )}

          {/* Estimated Delivery */}
          {estimatedDelivery && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Est. Delivery:</span>
              <span className="text-sm text-gray-900">
                {formatDate(estimatedDelivery)}
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2 mt-4 pt-4 border-t">
          <Link href={`/track-order?order=${orderId}`} className="flex-1">
            <Button variant="outline" className="w-full">
              <Package className="h-4 w-4 mr-2" />
              View Details
            </Button>
          </Link>
          
          {trackingUrl && (
            <Button
              variant="outline"
              onClick={() => window.open(trackingUrl, '_blank')}
              className="flex-1"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Track Online
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TrackingWidget;
